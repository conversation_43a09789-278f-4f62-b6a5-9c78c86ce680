{"$message_type":"diagnostic","message":"file not found for module `state`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":781,"byte_end":795,"line_start":20,"line_end":20,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod state;","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `state`, create file \"crates\\core\\src\\domain\\state.rs\" or \"crates\\core\\src\\domain\\state\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod state` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\mod.rs:20:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod state;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `state`, create file \"crates\\core\\src\\domain\\state.rs\" or \"crates\\core\\src\\domain\\state\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod state` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `events`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":796,"byte_end":811,"line_start":21,"line_end":21,"column_start":1,"column_end":16,"is_primary":true,"text":[{"text":"pub mod events;","highlight_start":1,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `events`, create file \"crates\\core\\src\\domain\\events.rs\" or \"crates\\core\\src\\domain\\events\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod events` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `events`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\mod.rs:21:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod events;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `events`, create file \"crates\\core\\src\\domain\\events.rs\" or \"crates\\core\\src\\domain\\events\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod events` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `errors`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":812,"byte_end":827,"line_start":22,"line_end":22,"column_start":1,"column_end":16,"is_primary":true,"text":[{"text":"pub mod errors;","highlight_start":1,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `errors`, create file \"crates\\core\\src\\domain\\errors.rs\" or \"crates\\core\\src\\domain\\errors\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod errors` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `errors`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\mod.rs:22:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod errors;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `errors`, create file \"crates\\core\\src\\domain\\errors.rs\" or \"crates\\core\\src\\domain\\errors\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod errors` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `application`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":1155,"byte_end":1175,"line_start":27,"line_end":27,"column_start":1,"column_end":21,"is_primary":true,"text":[{"text":"pub mod application;","highlight_start":1,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `application`, create file \"crates\\core\\src\\application.rs\" or \"crates\\core\\src\\application\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod application` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `application`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:27:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod application;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `application`, create file \"crates\\core\\src\\application.rs\" or \"crates\\core\\src\\application\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod application` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `infrastructure`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":1241,"byte_end":1264,"line_start":30,"line_end":30,"column_start":1,"column_end":24,"is_primary":true,"text":[{"text":"pub mod infrastructure;","highlight_start":1,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `infrastructure`, create file \"crates\\core\\src\\infrastructure.rs\" or \"crates\\core\\src\\infrastructure\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod infrastructure` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `infrastructure`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:30:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod infrastructure;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `infrastructure`, create file \"crates\\core\\src\\infrastructure.rs\" or \"crates\\core\\src\\infrastructure\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod infrastructure` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the value of the associated type `Props` in `component::Component` must be specified","code":{"code":"E0191","explanation":"An associated type wasn't specified for a trait object.\n\nErroneous code example:\n\n```compile_fail,E0191\ntrait Trait {\n    type Bar;\n}\n\ntype Foo = dyn Trait; // error: the value of the associated type `Bar` (from\n                      //        the trait `Trait`) must be specified\n```\n\nTrait objects need to have all associated types specified. Please verify that\nall associated types of the trait were specified and the correct trait was used.\nExample:\n\n```\ntrait Trait {\n    type Bar;\n}\n\ntype Foo = dyn Trait<Bar=i32>; // ok!\n```\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\component.rs","byte_start":1060,"byte_end":1077,"line_start":32,"line_end":32,"column_start":5,"column_end":22,"is_primary":false,"text":[{"text":"    type Props: Props;","highlight_start":5,"highlight_end":22}],"label":"`Props` defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\component.rs","byte_start":3714,"byte_end":3723,"line_start":108,"line_end":108,"column_start":44,"column_end":53,"is_primary":true,"text":[{"text":"    fn store(&mut self, component: Box<dyn Component>) -> Result<(), ComponentError>;","highlight_start":44,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"specify the associated type","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\component.rs","byte_start":3714,"byte_end":3723,"line_start":108,"line_end":108,"column_start":44,"column_end":53,"is_primary":true,"text":[{"text":"    fn store(&mut self, component: Box<dyn Component>) -> Result<(), ComponentError>;","highlight_start":44,"highlight_end":53}],"label":null,"suggested_replacement":"Component<Props = Type>","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0191]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the value of the associated type `Props` in `component::Component` must be specified\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\component.rs:108:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m32\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    type Props: Props;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`Props` defined here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn store(&mut self, component: Box<dyn Component>) -> Result<(), ComponentError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: specify the associated type: `Component<Props = Type>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the value of the associated type `Props` in `component::Component` must be specified","code":{"code":"E0191","explanation":"An associated type wasn't specified for a trait object.\n\nErroneous code example:\n\n```compile_fail,E0191\ntrait Trait {\n    type Bar;\n}\n\ntype Foo = dyn Trait; // error: the value of the associated type `Bar` (from\n                      //        the trait `Trait`) must be specified\n```\n\nTrait objects need to have all associated types specified. Please verify that\nall associated types of the trait were specified and the correct trait was used.\nExample:\n\n```\ntrait Trait {\n    type Bar;\n}\n\ntype Foo = dyn Trait<Bar=i32>; // ok!\n```\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\component.rs","byte_start":1060,"byte_end":1077,"line_start":32,"line_end":32,"column_start":5,"column_end":22,"is_primary":false,"text":[{"text":"    type Props: Props;","highlight_start":5,"highlight_end":22}],"label":"`Props` defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\component.rs","byte_start":3847,"byte_end":3856,"line_start":111,"line_end":111,"column_start":51,"column_end":60,"is_primary":true,"text":[{"text":"    fn get(&self, id: ComponentId) -> Option<&dyn Component>;","highlight_start":51,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"specify the associated type","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\component.rs","byte_start":3847,"byte_end":3856,"line_start":111,"line_end":111,"column_start":51,"column_end":60,"is_primary":true,"text":[{"text":"    fn get(&self, id: ComponentId) -> Option<&dyn Component>;","highlight_start":51,"highlight_end":60}],"label":null,"suggested_replacement":"Component<Props = Type>","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0191]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the value of the associated type `Props` in `component::Component` must be specified\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\component.rs:111:51\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m32\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    type Props: Props;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`Props` defined here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get(&self, id: ComponentId) -> Option<&dyn Component>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: specify the associated type: `Component<Props = Type>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait `Props` is not dyn compatible","code":{"code":"E0038","explanation":"For any given trait `Trait` there may be a related _type_ called the _trait\nobject type_ which is typically written as `dyn Trait`. In earlier editions of\nRust, trait object types were written as plain `Trait` (just the name of the\ntrait, written in type positions) but this was a bit too confusing, so we now\nwrite `dyn Trait`.\n\nSome traits are not allowed to be used as trait object types. The traits that\nare allowed to be used as trait object types are called \"dyn-compatible\"[^1]\ntraits. Attempting to use a trait object type for a trait that is not\ndyn-compatible will trigger error E0038.\n\nTwo general aspects of trait object types give rise to the restrictions:\n\n  1. Trait object types are dynamically sized types (DSTs), and trait objects of\n     these types can only be accessed through pointers, such as `&dyn Trait` or\n     `Box<dyn Trait>`. The size of such a pointer is known, but the size of the\n     `dyn Trait` object pointed-to by the pointer is _opaque_ to code working\n     with it, and different trait objects with the same trait object type may\n     have different sizes.\n\n  2. The pointer used to access a trait object is paired with an extra pointer\n     to a \"virtual method table\" or \"vtable\", which is used to implement dynamic\n     dispatch to the object's implementations of the trait's methods. There is a\n     single such vtable for each trait implementation, but different trait\n     objects with the same trait object type may point to vtables from different\n     implementations.\n\nThe specific conditions that violate dyn-compatibility follow, most of which\nrelate to missing size information and vtable polymorphism arising from these\naspects.\n\n[^1]: Formerly known as \"object-safe\".\n\n### The trait requires `Self: Sized`\n\nTraits that are declared as `Trait: Sized` or which otherwise inherit a\nconstraint of `Self:Sized` are not dyn-compatible.\n\nThe reasoning behind this is somewhat subtle. It derives from the fact that Rust\nrequires (and defines) that every trait object type `dyn Trait` automatically\nimplements `Trait`. Rust does this to simplify error reporting and ease\ninteroperation between static and dynamic polymorphism. For example, this code\nworks:\n\n```\ntrait Trait {\n}\n\nfn static_foo<T:Trait + ?Sized>(b: &T) {\n}\n\nfn dynamic_bar(a: &dyn Trait) {\n    static_foo(a)\n}\n```\n\nThis code works because `dyn Trait`, if it exists, always implements `Trait`.\n\nHowever as we know, any `dyn Trait` is also unsized, and so it can never\nimplement a sized trait like `Trait:Sized`. So, rather than allow an exception\nto the rule that `dyn Trait` always implements `Trait`, Rust chooses to prohibit\nsuch a `dyn Trait` from existing at all.\n\nOnly unsized traits are considered dyn-compatible.\n\nGenerally, `Self: Sized` is used to indicate that the trait should not be used\nas a trait object. If the trait comes from your own crate, consider removing\nthis restriction.\n\n### Method references the `Self` type in its parameters or return type\n\nThis happens when a trait has a method like the following:\n\n```\ntrait Trait {\n    fn foo(&self) -> Self;\n}\n\nimpl Trait for String {\n    fn foo(&self) -> Self {\n        \"hi\".to_owned()\n    }\n}\n\nimpl Trait for u8 {\n    fn foo(&self) -> Self {\n        1\n    }\n}\n```\n\n(Note that `&self` and `&mut self` are okay, it's additional `Self` types which\ncause this problem.)\n\nIn such a case, the compiler cannot predict the return type of `foo()` in a\nsituation like the following:\n\n```compile_fail,E0038\ntrait Trait {\n    fn foo(&self) -> Self;\n}\n\nfn call_foo(x: Box<dyn Trait>) {\n    let y = x.foo(); // What type is y?\n    // ...\n}\n```\n\nIf only some methods aren't dyn-compatible, you can add a `where Self: Sized`\nbound on them to mark them as explicitly unavailable to trait objects. The\nfunctionality will still be available to all other implementers, including\n`Box<dyn Trait>` which is itself sized (assuming you `impl Trait for Box<dyn\nTrait>`).\n\n```\ntrait Trait {\n    fn foo(&self) -> Self where Self: Sized;\n    // more functions\n}\n```\n\nNow, `foo()` can no longer be called on a trait object, but you will now be\nallowed to make a trait object, and that will be able to call any dyn-compatible\nmethods. With such a bound, one can still call `foo()` on types implementing\nthat trait that aren't behind trait objects.\n\n### Method has generic type parameters\n\nAs mentioned before, trait objects contain pointers to method tables. So, if we\nhave:\n\n```\ntrait Trait {\n    fn foo(&self);\n}\n\nimpl Trait for String {\n    fn foo(&self) {\n        // implementation 1\n    }\n}\n\nimpl Trait for u8 {\n    fn foo(&self) {\n        // implementation 2\n    }\n}\n// ...\n```\n\nAt compile time each implementation of `Trait` will produce a table containing\nthe various methods (and other items) related to the implementation, which will\nbe used as the virtual method table for a `dyn Trait` object derived from that\nimplementation.\n\nThis works fine, but when the method gains generic parameters, we can have a\nproblem.\n\nUsually, generic parameters get _monomorphized_. For example, if I have\n\n```\nfn foo<T>(x: T) {\n    // ...\n}\n```\n\nThe machine code for `foo::<u8>()`, `foo::<bool>()`, `foo::<String>()`, or any\nother instantiation is different. Hence the compiler generates the\nimplementation on-demand. If you call `foo()` with a `bool` parameter, the\ncompiler will only generate code for `foo::<bool>()`. When we have additional\ntype parameters, the number of monomorphized implementations the compiler\ngenerates does not grow drastically, since the compiler will only generate an\nimplementation if the function is called with fully concrete arguments\n(i.e., arguments which do not contain any generic parameters).\n\nHowever, with trait objects we have to make a table containing _every_ object\nthat implements the trait. Now, if it has type parameters, we need to add\nimplementations for every type that implements the trait, and there could\ntheoretically be an infinite number of types.\n\nFor example, with:\n\n```\ntrait Trait {\n    fn foo<T>(&self, on: T);\n    // more methods\n}\n\nimpl Trait for String {\n    fn foo<T>(&self, on: T) {\n        // implementation 1\n    }\n}\n\nimpl Trait for u8 {\n    fn foo<T>(&self, on: T) {\n        // implementation 2\n    }\n}\n\n// 8 more implementations\n```\n\nNow, if we have the following code:\n\n```compile_fail,E0038\n# trait Trait { fn foo<T>(&self, on: T); }\n# impl Trait for String { fn foo<T>(&self, on: T) {} }\n# impl Trait for u8 { fn foo<T>(&self, on: T) {} }\n# impl Trait for bool { fn foo<T>(&self, on: T) {} }\n# // etc.\nfn call_foo(thing: Box<dyn Trait>) {\n    thing.foo(true); // this could be any one of the 8 types above\n    thing.foo(1);\n    thing.foo(\"hello\");\n}\n```\n\nWe don't just need to create a table of all implementations of all methods of\n`Trait`, we need to create such a table, for each different type fed to\n`foo()`. In this case this turns out to be (10 types implementing `Trait`)\\*(3\ntypes being fed to `foo()`) = 30 implementations!\n\nWith real world traits these numbers can grow drastically.\n\nTo fix this, it is suggested to use a `where Self: Sized` bound similar to the\nfix for the sub-error above if you do not intend to call the method with type\nparameters:\n\n```\ntrait Trait {\n    fn foo<T>(&self, on: T) where Self: Sized;\n    // more methods\n}\n```\n\nIf this is not an option, consider replacing the type parameter with another\ntrait object (e.g., if `T: OtherTrait`, use `on: Box<dyn OtherTrait>`). If the\nnumber of types you intend to feed to this method is limited, consider manually\nlisting out the methods of different types.\n\n### Method has no receiver\n\nMethods that do not take a `self` parameter can't be called since there won't be\na way to get a pointer to the method table for them.\n\n```\ntrait Foo {\n    fn foo() -> u8;\n}\n```\n\nThis could be called as `<Foo as Foo>::foo()`, which would not be able to pick\nan implementation.\n\nAdding a `Self: Sized` bound to these methods will generally make this compile.\n\n```\ntrait Foo {\n    fn foo() -> u8 where Self: Sized;\n}\n```\n\n### Trait contains associated constants\n\nJust like static functions, associated constants aren't stored on the method\ntable. If the trait or any subtrait contain an associated constant, they are not\ndyn compatible.\n\n```compile_fail,E0038\ntrait Foo {\n    const X: i32;\n}\n\nimpl dyn Foo {}\n```\n\nA simple workaround is to use a helper method instead:\n\n```\ntrait Foo {\n    fn x(&self) -> i32;\n}\n```\n\n### Trait uses `Self` as a type parameter in the supertrait listing\n\nThis is similar to the second sub-error, but subtler. It happens in situations\nlike the following:\n\n```compile_fail,E0038\ntrait Super<A: ?Sized> {}\n\ntrait Trait: Super<Self> {\n}\n\nstruct Foo;\n\nimpl Super<Foo> for Foo{}\n\nimpl Trait for Foo {}\n\nfn main() {\n    let x: Box<dyn Trait>;\n}\n```\n\nHere, the supertrait might have methods as follows:\n\n```\ntrait Super<A: ?Sized> {\n    fn get_a(&self) -> &A; // note that this is dyn-compatible!\n}\n```\n\nIf the trait `Trait` was deriving from something like `Super<String>` or\n`Super<T>` (where `Foo` itself is `Foo<T>`), this is okay, because given a type\n`get_a()` will definitely return an object of that type.\n\nHowever, if it derives from `Super<Self>`, even though `Super` is\ndyn-compatible, the method `get_a()` would return an object of unknown type when\ncalled on the function. `Self` type parameters let us make dyn-compatible traits\nno longer compatible, so they are forbidden when specifying supertraits.\n\nThere's no easy fix for this. Generally, code will need to be refactored so that\nyou no longer need to derive from `Super<Self>`.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\props.rs","byte_start":730,"byte_end":735,"line_start":22,"line_end":22,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"    fn validate_props(&self, props: &dyn Props) -> Result<(), PropsError>;","highlight_start":42,"highlight_end":47}],"label":"`Props` is not dyn compatible","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for a trait to be dyn compatible it needs to allow building a vtable\nfor more information, visit <https://doc.rust-lang.org/reference/items/traits.html#dyn-compatibility>","code":null,"level":"note","spans":[{"file_name":"crates\\core\\src\\domain\\props.rs","byte_start":308,"byte_end":313,"line_start":11,"line_end":11,"column_start":11,"column_end":16,"is_primary":false,"text":[{"text":"pub trait Props: Clone + PartialEq + Send + Sync + 'static {","highlight_start":11,"highlight_end":16}],"label":"this trait is not dyn compatible...","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\props.rs","byte_start":323,"byte_end":332,"line_start":11,"line_end":11,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"pub trait Props: Clone + PartialEq + Send + Sync + 'static {","highlight_start":26,"highlight_end":35}],"label":"...because it uses `Self` as a type parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the following types implement `Props`:\n  domain::props::DynamicProps\n  domain::props::TextProps\nconsider defining an enum where each variant holds one of these types,\nimplementing `Props` for this new enum and using it instead","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`Props` may be implemented in other crates; if you want to support your users passing their own types here, you can't refer to a specific type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using an opaque type instead","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\props.rs","byte_start":726,"byte_end":730,"line_start":22,"line_end":22,"column_start":38,"column_end":42,"is_primary":true,"text":[{"text":"    fn validate_props(&self, props: &dyn Props) -> Result<(), PropsError>;","highlight_start":38,"highlight_end":42}],"label":null,"suggested_replacement":"impl ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0038]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait `Props` is not dyn compatible\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\props.rs:22:42\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn validate_props(&self, props: &dyn Props) -> Result<(), PropsError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`Props` is not dyn compatible\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: for a trait to be dyn compatible it needs to allow building a vtable\u001b[0m\n\u001b[0m      for more information, visit <https://doc.rust-lang.org/reference/items/traits.html#dyn-compatibility>\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\props.rs:11:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Props: Clone + PartialEq + Send + Sync + 'static {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m...because it uses `Self` as a type parameter\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis trait is not dyn compatible...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following types implement `Props`:\u001b[0m\n\u001b[0m             domain::props::DynamicProps\u001b[0m\n\u001b[0m             domain::props::TextProps\u001b[0m\n\u001b[0m           consider defining an enum where each variant holds one of these types,\u001b[0m\n\u001b[0m           implementing `Props` for this new enum and using it instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `Props` may be implemented in other crates; if you want to support your users passing their own types here, you can't refer to a specific type\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider using an opaque type instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    fn validate_props(&self, props: &\u001b[0m\u001b[0m\u001b[38;5;9mdyn \u001b[0m\u001b[0mProps) -> Result<(), PropsError>;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    fn validate_props(&self, props: &\u001b[0m\u001b[0m\u001b[38;5;10mimpl \u001b[0m\u001b[0mProps) -> Result<(), PropsError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 8 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 8 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0038, E0191, E0583.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0038, E0191, E0583.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0038`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0038`.\u001b[0m\n"}
