//! Rendering system for TUI React
//!
//! This module provides utilities for rendering components and managing
//! the render loop.

use crate::Component;
use ratatui::{Frame, layout::Rect};

/// A renderer that can render components to a frame
pub struct Renderer;

impl Renderer {
    /// Create a new renderer
    pub fn new() -> Self {
        Self
    }

    /// Render a component to the given frame and area
    pub fn render_component<C>(&self, component: &C, frame: &mut Frame, area: Rect)
    where
        C: Component,
    {
        component.render(frame, area);
    }
}

impl Default for Renderer {
    fn default() -> Self {
        Self::new()
    }
}
