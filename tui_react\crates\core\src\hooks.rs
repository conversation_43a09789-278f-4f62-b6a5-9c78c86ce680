//! Hooks system for TUI React
//! 
//! This module provides React-like hooks for managing state and side effects
//! in TUI components.

/// A simple state hook similar to React's useState
pub struct UseState<T> {
    value: T,
}

impl<T> UseState<T> {
    /// Create a new state hook with an initial value
    pub fn new(initial: T) -> Self {
        Self { value: initial }
    }

    /// Get the current value
    pub fn get(&self) -> &T {
        &self.value
    }

    /// Set a new value
    pub fn set(&mut self, value: T) {
        self.value = value;
    }
}
